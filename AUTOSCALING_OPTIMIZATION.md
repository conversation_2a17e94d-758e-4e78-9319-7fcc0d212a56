# Optimizaciones de Health Check y Auto Scaling

## Resumen de Cambios Implementados

### 1. Health Check Optimizado (ALB Target Group)

**Antes:**
```terraform
health_check {
  enabled             = true
  interval            = 300  # 5 minutos
  healthy_threshold   = 3
  protocol            = "HTTP"
  path                = var.health_check_url
  port                = "${var.application_port}"
  timeout             = 60   # 1 minuto
  unhealthy_threshold = 3
}
```

**Después:**
```terraform
health_check {
  enabled             = true
  interval            = 15   # 15 segundos
  healthy_threshold   = 2    # Reducido para detección más rápida
  protocol            = "HTTP"
  matcher             = "200-399"  # Agregado matcher
  path                = var.health_check_url
  port                = "${var.application_port}"
  timeout             = 3    # 3 segundos
  unhealthy_threshold = 3    # Mantener para evitar falsos positivos
}
```

### 2. ECS Service Health Check Grace Period

**Antes:** Sin configurar (default 0)
**Después:** 120 segundos

```terraform
resource "aws_ecs_service" "svc" {
  # ... otras configuraciones
  health_check_grace_period_seconds = 120
  desired_count = var.auto_scaling_policy != null ? var.auto_scaling_policy.min : 1
}
```

### 3. Auto Scaling Policies Optimizadas

#### Desarrollo (dev)
| Métrica | Antes | Después | Mejora |
|---------|-------|---------|--------|
| Min Instances | 1 | 1 | - |
| Max Instances | 10 | 5 | 50% reducción de costos |
| Target RPS | 150 | 100 | Escalado más responsivo |
| Scale In Cooldown | 10s | 60s | Mayor estabilidad |
| Scale Out Cooldown | 10s | 30s | Respuesta más rápida |
| CPU Target | - | 60% | Nueva métrica |

#### Producción (prd)
| Métrica | Antes | Después | Mejora |
|---------|-------|---------|--------|
| Min Instances | 8 | 3 | 62% reducción de costos base |
| Max Instances | 30 | 20 | 33% reducción de costos máximos |
| Target RPS | 150 | 120 | Escalado más responsivo |
| Scale In Cooldown | 10s | 300s (5min) | Prevención de flapping |
| Scale Out Cooldown | 10s | 60s | Balance entre respuesta y estabilidad |
| CPU Target | - | 65% | Nueva métrica |

### 4. Escalado Dual (RPS + CPU)

Se agregó una segunda política de auto scaling basada en CPU utilization:

```terraform
resource "aws_appautoscaling_policy" "ecs_cpu" {
  count = var.auto_scaling_policy != null ? 1 : 0

  name               = "scale-cpu-${var.name}"
  policy_type        = "TargetTrackingScaling"
  
  target_tracking_scaling_policy_configuration {
    predefined_metric_specification {
      predefined_metric_type = "ECSServiceAverageCPUUtilization"
    }
    target_value       = var.auto_scaling_policy.cpu_target
    scale_in_cooldown  = var.auto_scaling_policy.in_cooldown
    scale_out_cooldown = var.auto_scaling_policy.out_cooldown
  }
}
```

## Beneficios Esperados

### Costos
- **DEV:** Hasta 50% reducción en costos máximos
- **PRD:** Hasta 62% reducción en costos base, 33% en picos

### Performance
- **Detección de problemas:** 20x más rápida (15s vs 300s)
- **Escalado:** Más responsivo con targets más bajos
- **Estabilidad:** Cooldowns optimizados previenen flapping

### Confiabilidad
- **Health checks:** Más frecuentes y precisos
- **Grace period:** Tiempo adecuado para inicialización
- **Dual metrics:** Escalado basado en RPS y CPU

## Servicios Afectados

Actualmente solo `bch-wsbcochile` tiene auto scaling configurado.
Para habilitar en otros servicios, agregar configuración en `ecr-repos.tf`:

```terraform
"nombre-servicio" = {
  min          = 1
  max          = 5
  target       = 100
  in_cooldown  = 60
  out_cooldown = 30
  cpu_target   = 60
}
```
