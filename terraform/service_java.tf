locals {
  bch_apis = {
    "bch-account" = {
      image  = "ubuntu"
      cpu    = "256"
      memory = "512"
      ports  = 8080
      health_check_url = "/account/actuator/health"
      enable_efs       = false 
      use_internal_lb = false
    },
    "bch-general" = {
      image  = "ubuntu"
      cpu    = "256"
      memory = "512"
      ports  = 8080
      health_check_url = "/general/actuator/health"
      enable_efs       = false
      use_internal_lb = false 
    },
    "bch-maintenace" = {
      image  = "ubuntu"
      cpu    = "256"
      memory = "512"
      ports  = 8080
      health_check_url = "/actuator/health"
      enable_efs       = false 
      use_internal_lb = true
    },
     "bch-listengkv" = {
      image  = "ubuntu"
      cpu    = "256"
      memory = "512"
      ports  = 8080
      health_check_url = "/listengkv/actuator/health"
      enable_efs       = false 
      use_internal_lb = true
    },
     "bch-redispatch" = {
      image  = "ubuntu"
      cpu    = "256"
      memory = "512"
      ports  = 8080
      health_check_url = "/redispatch/actuator/health"
      enable_efs       = false 
      use_internal_lb = true
    },
     "bch-wsbcochile" = {
      image  = "ubuntu"
      cpu    = "256"
      memory = "512"
      ports  = 8080
      health_check_url = "/actuator/health"
      enable_efs       = false 
      use_internal_lb = false
    },
     "bch-sendstatusbcochile" = {
      image  = "ubuntu"
      cpu    = "256"
      memory = "512"
      ports  = 8080
      health_check_url = "/sendstatusbcochile/actuator/health"
      enable_efs       = false 
      use_internal_lb = true
    },
    "bch-report"= {
      image  = "ubuntu"
      cpu    = "256"
      memory = "512"
      ports  = 8080
      health_check_url = "/report/actuator/health"
      enable_efs       = true 
      use_internal_lb = false
      efs_mount_path   = "/reports"
    },
    "bch-monitoringkpi"= {
      image  = "ubuntu"
      cpu    = "256"
      memory = "512"
      ports  = 8080
      health_check_url = "/monitoringKpi/actuator/health"
      enable_efs       = false 
      use_internal_lb = false
    }
  }
}

module "java_api_services" {
  for_each = local.bch_apis
  source   = "./modules/api-service"

  name                = each.key
  image               = each.value.image
  cpu                 = each.value.cpu
  memory              = each.value.memory
  application_port    = each.value.ports
  health_check_url    = each.value.health_check_url

  vpc_id              = local.vpc_id
  subnet_ids          = local.subnet_private_ids

  cluster_name        = local.cluster_name

  alb_listener_arn = lookup(each.value, "use_internal_lb", false) ? local.alb_http_listener_arn : local.albext_https_listener_arn


  #alb_listener_arn    = local.albext_https_listener_arn
  envzone_name        = local.envzone_name
  envzone_zone_id     = local.envzone_zone_id
  alb_dns_name = lookup(each.value, "use_internal_lb", false) ? local.alb_dns_name : local.albext_dns_name
  #alb_dns_name        = local.albext_dns_name
  private_cidr_block  = "10.0.0.0/8"
  log_retention_days  = local.retention_in_days

  # EFS Configuration (optional per API)
  enable_efs       = lookup(each.value, "enable_efs", false)
  efs_mount_path   = lookup(each.value, "efs_mount_path", "/mnt/reports")

  # Auto Scaling Configuration (optional per API)
  auto_scaling_policy = lookup(local.auto_scaling_policies, each.key, null)

}
