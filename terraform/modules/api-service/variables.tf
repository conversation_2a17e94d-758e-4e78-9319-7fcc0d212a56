
variable "name" { type = string }
variable "image" { type = string }
variable "cpu" { type = string }
variable "memory" { type = string }
variable "application_port" { type = number }

variable "vpc_id" { type = string }
variable "subnet_ids" { type = list(string) }
variable "private_cidr_block" { type = string }
variable "health_check_url" { type = string }

variable "cluster_name" { type = string }
variable "alb_listener_arn" { type = string }
variable "envzone_name" { type = string }
variable "envzone_zone_id" { type = string }
variable "alb_dns_name" { type = string }

variable "log_retention_days" { type = number }

variable "auto_scaling_policy" {
  type = object({
    min          = number
    max          = number
    target       = number
    in_cooldown  = number
    out_cooldown = number
    cpu_target   = optional(number, 60)
  })
  default  = null
  nullable = true
}

# EFS Configuration
variable "enable_efs" {
  type        = bool
  default     = false
  description = "Whether to create and mount an EFS file system"
}

variable "efs_mount_path" {
  type        = string
  default     = "/reports"
  description = "Path where EFS will be mounted in the container"
}

variable "efs_performance_mode" {
  type        = string
  default     = "generalPurpose"
  description = "EFS performance mode (generalPurpose or maxIO)"
  validation {
    condition     = contains(["generalPurpose", "maxIO"], var.efs_performance_mode)
    error_message = "EFS performance mode must be either 'generalPurpose' or 'maxIO'."
  }
}

variable "efs_throughput_mode" {
  type        = string
  default     = "bursting"
  description = "EFS throughput mode (bursting or provisioned)"
  validation {
    condition     = contains(["bursting", "provisioned"], var.efs_throughput_mode)
    error_message = "EFS throughput mode must be either 'bursting' or 'provisioned'."
  }
}
