locals {
  ecr_repositories_all = {
    dev = [ "bch-account" , "bch-general" , "bch-maintenace" , "bch-listengkv" , "bch-redispatch" , "bch-wsbcochile" , "bch-sendstatusbcochile" , "bch-report" , "bch-monitoringkpi"]
    stg = []
    prd = ["bch-account" , "bch-general" , "bch-maintenace" , "bch-listengkv" , "bch-redispatch" , "bch-wsbcochile" , "bch-sendstatusbcochile" , "bch-report" , "bch-monitoringkpi"]
  }
  ecr_repositories = local.ecr_repositories_all[local.env]

  #############################################
  # Auto scaling policies (per environment)
  #############################################
  auto_scaling_policies_all = {
    dev = {
      "bch-wsbcochile" = {
        min          = 1
        max          = 5
        target       = 100
        in_cooldown  = 60
        out_cooldown = 30
        cpu_target   = 60
      }
    }
    stg = {
    }
    prd = {
      "bch-wsbcochile" = {
        min          = 3
        max          = 20
        target       = 120
        in_cooldown  = 300
        out_cooldown = 60
        cpu_target   = 65
      }
    }
  }
  auto_scaling_policies = local.auto_scaling_policies_all[local.env]
}
module "ecr" {
  for_each        = toset(local.ecr_repositories)
  source          = "./modules/ecr-repository"
  repository_name = each.key
}