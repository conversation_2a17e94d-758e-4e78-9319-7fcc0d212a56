locals {
  ecr_repositories_all = {
    dev = [ "bch-account" , "bch-general" , "bch-maintenace" , "bch-listengkv" , "bch-redispatch" , "bch-wsbcochile" , "bch-sendstatusbcochile" , "bch-report" , "bch-monitoringkpi"]
    stg = []
    prd = ["bch-account" , "bch-general" , "bch-maintenace" , "bch-listengkv" , "bch-redispatch" , "bch-wsbcochile" , "bch-sendstatusbcochile" , "bch-report" , "bch-monitoringkpi"]
  }
  ecr_repositories = local.ecr_repositories_all[local.env]

  #############################################
  # Auto scaling policies (per environment)
  #############################################
  auto_scaling_policies_all = {
    dev = {
      "bch-wsbcochile" = {
        min          = 1
        max          = 10
        target       = 150
        in_cooldown  = 10
        out_cooldown = 10
      }
    }
    stg = {
    }
    prd = {
      "bch-wsbcochile" = {
        min          = 8
        max          = 30
        target       = 150
        in_cooldown  = 10
        out_cooldown = 10
      }
    }
  }
  auto_scaling_policies = local.auto_scaling_policies_all[local.env]
}
module "ecr" {
  for_each        = toset(local.ecr_repositories)
  source          = "./modules/ecr-repository"
  repository_name = each.key
}